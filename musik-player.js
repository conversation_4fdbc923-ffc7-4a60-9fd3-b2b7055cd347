/**
 * Musik Player JavaScript
 * Hauptfunktionalität für die Musikseite
 */

class MusikPlayer {
    constructor() {
        this.songs = [];
        this.currentSongIndex = -1;
        this.isPlaying = false;
        this.isShuffleMode = false;
        this.isRepeatMode = false;
        this.currentSort = { column: 'nummer', direction: 'asc' };
        this.hiddenSongs = new Set();
        this.filteredSongs = [];
        
        // Audio Element
        this.audio = document.getElementById('audio-player');
        
        // DOM Elements
        this.songTableBody = document.getElementById('song-table-body');
        this.mobileSongList = document.getElementById('mobile-song-list');
        this.currentSongTitle = document.getElementById('current-song-title');
        this.currentArtist = document.getElementById('current-artist');
        this.currentAlbum = document.getElementById('current-album');
        this.playPauseBtn = document.getElementById('play-pause-btn');
        this.progressFill = document.getElementById('progress-fill');
        this.progressHandle = document.getElementById('progress-handle');
        this.currentTimeDisplay = document.getElementById('current-time');
        this.totalTimeDisplay = document.getElementById('total-time');
        this.lyricsContent = document.getElementById('lyrics-content');
        this.lyricsTranslation = document.getElementById('lyrics-translation');
        this.lyricsInfo = document.getElementById('lyrics-info');
        this.lyricsTabs = document.getElementById('lyrics-tabs');
        
        // Mobile Elements
        this.miniPlayer = document.getElementById('mini-player');
        this.miniSongTitle = document.getElementById('mini-song-title');
        this.miniPlayPause = document.getElementById('mini-play-pause');
        this.modalOverlay = document.getElementById('modal-overlay');
        this.modalPlayer = document.getElementById('modal-player');
        
        this.init();
    }
    
    init() {
        this.loadSongs();
        this.setupEventListeners();
        this.renderSongTable();
        this.renderMobileSongList();
        this.setupFilters();
    }
    
    loadSongs() {
        // Songs aus musik-data.js laden und mit Index versehen
        this.songs = MUSIK_DATEN.map((song, index) => ({
            ...song,
            id: index + 1,
            nummer: index + 1,
            isHidden: false
        }));
        this.filteredSongs = [...this.songs];
    }
    
    setupEventListeners() {
        // Audio Events
        this.audio.addEventListener('loadedmetadata', () => this.updateDuration());
        this.audio.addEventListener('timeupdate', () => this.updateProgress());
        this.audio.addEventListener('ended', () => this.nextSong());
        this.audio.addEventListener('error', (e) => this.handleAudioError(e));
        
        // Player Controls
        this.playPauseBtn.addEventListener('click', () => this.togglePlayPause());
        document.getElementById('prev-btn').addEventListener('click', () => this.previousSong());
        document.getElementById('next-btn').addEventListener('click', () => this.nextSong());
        document.getElementById('shuffle-btn').addEventListener('click', () => this.toggleShuffle());
        document.getElementById('repeat-btn').addEventListener('click', () => this.toggleRepeat());
        
        // Progress Bar
        const progressContainer = document.querySelector('.progress-bar-container');
        progressContainer.addEventListener('click', (e) => this.seekTo(e));
        
        // Table Sorting
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', () => this.sortTable(header.dataset.column));
        });
        
        // Filter Controls
        document.getElementById('genre-filter').addEventListener('change', () => this.applyFilters());
        document.getElementById('sprache-filter').addEventListener('change', () => this.applyFilters());
        document.getElementById('reset-filters').addEventListener('click', () => this.resetFilters());

        // Lyrics Tabs
        this.setupLyricsTabs();

        // Album Filter Click
        document.getElementById('current-album').addEventListener('click', () => this.filterByCurrentAlbum());

        // Mobile Filter Controls
        document.getElementById('mobile-genre-filter').addEventListener('change', () => this.applyMobileFilters());
        document.getElementById('mobile-sprache-filter').addEventListener('change', () => this.applyMobileFilters());
        document.getElementById('mobile-album-filter').addEventListener('change', () => this.applyMobileFilters());

        // Mobile Control Buttons
        document.getElementById('mobile-shuffle-btn').addEventListener('click', () => this.toggleShuffle());
        document.getElementById('mobile-repeat-btn').addEventListener('click', () => this.toggleRepeat());
        document.getElementById('mobile-reset-filters').addEventListener('click', () => this.resetFilters());
        
        // Header Actions
        document.getElementById('toggle-all-visibility').addEventListener('click', () => this.toggleAllVisibility());
        document.getElementById('download-all-visible').addEventListener('click', () => this.downloadAllVisible());
        document.getElementById('download-current-btn').addEventListener('click', () => this.downloadCurrent());
        
        // Mobile Events
        this.miniPlayPause.addEventListener('click', () => this.togglePlayPause());
        document.getElementById('mini-expand').addEventListener('click', () => this.openMobilePlayer());
        document.getElementById('modal-close').addEventListener('click', () => this.closeMobilePlayer());
        this.modalOverlay.addEventListener('click', (e) => {
            if (e.target === this.modalOverlay) this.closeMobilePlayer();
        });
        
        // Keyboard Shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));

        // Window Resize für Mini-Player
        window.addEventListener('resize', () => this.handleResize());
    }
    
    renderSongTable() {
        this.songTableBody.innerHTML = '';
        
        this.filteredSongs.forEach((song, index) => {
            const row = document.createElement('tr');
            row.dataset.songId = song.id;
            row.className = song.isHidden ? 'hidden' : '';
            
            row.innerHTML = `
                <td>${song.nummer}</td>
                <td class="song-title">${song.titel}</td>
                <td class="song-album">${song.album}</td>
                <td class="song-genre">${song.genre}</td>
                <td class="song-sprache">${song.sprache}</td>
                <td>
                    <button class="action-btn hide-btn ${song.isHidden ? 'active' : ''}" 
                            data-song-id="${song.id}" title="Ausblenden/Einblenden">
                        <svg class="ear-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 2C8 2 5 5 5 9v6c0 1 1 2 2 2h1v-8c0-2.5 2.5-5 5-5s5 2.5 5 5v8h1c1 0 2-1 2-2V9c0-4-3-7-7-7z"/>
                            ${song.isHidden ? '<line x1="2" y1="2" x2="22" y2="22" class="strike-line"/>' : ''}
                        </svg>
                    </button>
                </td>
                <td>
                    <button class="action-btn download-btn" data-song-id="${song.id}" title="Herunterladen">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                            <polyline points="7,10 12,15 17,10"/>
                            <line x1="12" y1="15" x2="12" y2="3"/>
                        </svg>
                    </button>
                </td>
            `;
            
            // Event Listeners für die Zeile
            row.addEventListener('click', (e) => {
                if (!e.target.closest('.action-btn')) {
                    this.playSong(song.id - 1);
                }
            });
            
            // Event Listeners für Action Buttons
            const hideBtn = row.querySelector('.hide-btn');
            hideBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleSongVisibility(song.id);
            });
            
            const downloadBtn = row.querySelector('.download-btn');
            downloadBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.downloadSong(song.id);
            });
            
            this.songTableBody.appendChild(row);
        });
    }
    
    renderMobileSongList() {
        this.mobileSongList.innerHTML = '';

        this.filteredSongs.forEach((song, index) => {
            if (song.isHidden) return;

            const item = document.createElement('div');
            item.className = 'mobile-song-item';
            item.dataset.songId = song.id;

            item.innerHTML = `
                <div class="mobile-song-content">
                    <div class="mobile-album-cover" data-song-number="${song.nummer}">
                        <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                        </svg>
                    </div>
                    <div class="mobile-song-info">
                        <div class="mobile-song-title">${song.titel}</div>
                        <div class="mobile-song-album">${song.album}</div>
                    </div>
                </div>
            `;

            item.addEventListener('click', () => {
                this.playSong(song.id - 1);
                this.openMobilePlayer();
            });

            // Album Cover laden
            this.loadMobileAlbumCover(item.querySelector('.mobile-album-cover'), song.nummer);

            this.mobileSongList.appendChild(item);
        });
    }
    
    setupFilters() {
        // Genre Filter (Desktop)
        const genreFilter = document.getElementById('genre-filter');
        const mobileGenreFilter = document.getElementById('mobile-genre-filter');
        const genres = [...new Set(this.songs.map(song => song.genre))];

        genres.forEach(genre => {
            const option = document.createElement('option');
            option.value = genre.toLowerCase();
            option.textContent = genre;
            genreFilter.appendChild(option);

            const mobileOption = document.createElement('option');
            mobileOption.value = genre.toLowerCase();
            mobileOption.textContent = genre;
            mobileGenreFilter.appendChild(mobileOption);
        });

        // Sprache Filter (Desktop)
        const spracheFilter = document.getElementById('sprache-filter');
        const mobileSpracheFilter = document.getElementById('mobile-sprache-filter');
        const sprachen = [...new Set(this.songs.map(song => song.sprache))];

        sprachen.forEach(sprache => {
            const option = document.createElement('option');
            option.value = sprache.toLowerCase();
            option.textContent = sprache;
            spracheFilter.appendChild(option);

            const mobileOption = document.createElement('option');
            mobileOption.value = sprache.toLowerCase();
            mobileOption.textContent = sprache;
            mobileSpracheFilter.appendChild(mobileOption);
        });

        // Album Filter (Mobile only)
        const mobileAlbumFilter = document.getElementById('mobile-album-filter');
        const alben = [...new Set(this.songs.map(song => song.album))];

        alben.forEach(album => {
            const mobileOption = document.createElement('option');
            mobileOption.value = album.toLowerCase();
            mobileOption.textContent = album;
            mobileAlbumFilter.appendChild(mobileOption);
        });
    }
    
    applyFilters() {
        const genreFilter = document.getElementById('genre-filter').value;
        const spracheFilter = document.getElementById('sprache-filter').value;
        
        this.filteredSongs = this.songs.filter(song => {
            const genreMatch = genreFilter === 'alle' || song.genre.toLowerCase() === genreFilter;
            const spracheMatch = spracheFilter === 'alle' || song.sprache.toLowerCase() === spracheFilter;
            return genreMatch && spracheMatch;
        });
        
        this.renderSongTable();
        this.renderMobileSongList();
    }
    
    applyMobileFilters() {
        const genreFilter = document.getElementById('mobile-genre-filter').value;
        const spracheFilter = document.getElementById('mobile-sprache-filter').value;
        const albumFilter = document.getElementById('mobile-album-filter').value;

        this.filteredSongs = this.songs.filter(song => {
            const genreMatch = genreFilter === 'alle' || song.genre.toLowerCase() === genreFilter;
            const spracheMatch = spracheFilter === 'alle' || song.sprache.toLowerCase() === spracheFilter;
            const albumMatch = albumFilter === 'alle' || song.album.toLowerCase() === albumFilter;
            return genreMatch && spracheMatch && albumMatch;
        });

        this.renderMobileSongList();
    }

    resetFilters() {
        document.getElementById('genre-filter').value = 'alle';
        document.getElementById('sprache-filter').value = 'alle';
        document.getElementById('mobile-genre-filter').value = 'alle';
        document.getElementById('mobile-sprache-filter').value = 'alle';
        document.getElementById('mobile-album-filter').value = 'alle';
        this.filteredSongs = [...this.songs];
        this.renderSongTable();
        this.renderMobileSongList();
    }

    filterByCurrentAlbum() {
        if (this.currentSongIndex === -1) return;

        // Album-Filter auf das aktuelle Album setzen
        const currentSong = this.songs[this.currentSongIndex];
        const albumName = currentSong.album;

        // Alle Filter zurücksetzen
        document.getElementById('genre-filter').value = 'alle';
        document.getElementById('sprache-filter').value = 'alle';
        document.getElementById('mobile-genre-filter').value = 'alle';
        document.getElementById('mobile-sprache-filter').value = 'alle';
        document.getElementById('mobile-album-filter').value = albumName.toLowerCase();

        // Nach Album filtern
        this.filteredSongs = this.songs.filter(song => song.album === albumName);

        this.renderSongTable();
        this.renderMobileSongList();

        // Visuelles Feedback
        const albumElement = document.getElementById('current-album');
        albumElement.style.color = '#112736';
        albumElement.style.fontWeight = 'bold';

        setTimeout(() => {
            albumElement.style.color = '';
            albumElement.style.fontWeight = '';
        }, 1000);
    }
    
    sortTable(column) {
        if (this.currentSort.column === column) {
            this.currentSort.direction = this.currentSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
            this.currentSort.column = column;
            this.currentSort.direction = 'asc';
        }
        
        this.filteredSongs.sort((a, b) => {
            let aVal = a[column];
            let bVal = b[column];
            
            if (column === 'nummer') {
                aVal = parseInt(aVal);
                bVal = parseInt(bVal);
            } else {
                aVal = aVal.toString().toLowerCase();
                bVal = bVal.toString().toLowerCase();
            }
            
            if (aVal < bVal) return this.currentSort.direction === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.currentSort.direction === 'asc' ? 1 : -1;
            return 0;
        });
        
        this.updateSortIndicators();
        this.renderSongTable();
        this.renderMobileSongList();
    }
    
    updateSortIndicators() {
        document.querySelectorAll('.sort-indicator').forEach(indicator => {
            indicator.className = 'sort-indicator';
        });
        
        const activeHeader = document.querySelector(`[data-column="${this.currentSort.column}"] .sort-indicator`);
        if (activeHeader) {
            activeHeader.className = `sort-indicator ${this.currentSort.direction}`;
        }
    }
    
    playSong(index) {
        if (index < 0 || index >= this.songs.length) return;

        this.currentSongIndex = index;
        const song = this.songs[index];

        // Audio laden
        this.audio.src = `audio/${song.dateiname}`;
        this.audio.load();

        // UI aktualisieren
        this.updatePlayerInfo(song);
        this.updatePlayingState();

        // Abspielen
        this.audio.play().then(() => {
            this.isPlaying = true;
            this.updatePlayButton();

            // Mini-Player anzeigen wenn im mobilen Layout und Modal nicht geöffnet
            if (window.innerWidth <= 768 && this.modalOverlay.style.display !== 'flex') {
                this.miniPlayer.style.display = 'flex';
            }
        }).catch(error => {
            console.error('Fehler beim Abspielen:', error);
            this.handleAudioError(error);
        });
    }

    updatePlayerInfo(song) {
        this.currentSongTitle.textContent = song.titel;
        this.currentAlbum.textContent = song.album;
        this.miniSongTitle.textContent = song.titel;

        // Album Cover laden
        this.loadAlbumCover(song.nummer);

        // Lyrics und Übersetzung anzeigen
        this.updateLyricsDisplay(song);
    }

    loadAlbumCover(songNumber) {
        const albumCover = document.getElementById('album-cover');
        const miniAlbumCover = document.querySelector('.mini-album-cover');

        // Verschiedene Bildformate versuchen
        const possibleFormats = ['png', 'jpg', 'jpeg', 'webp'];
        let formatIndex = 0;

        const tryLoadImage = () => {
            if (formatIndex >= possibleFormats.length) {
                // Kein Bild gefunden - Platzhalter anzeigen
                this.showPlaceholderCover(albumCover, miniAlbumCover);
                return;
            }

            const format = possibleFormats[formatIndex];
            const coverPath = `bilder/album-covers/${songNumber}.${format}`;

            const img = new Image();
            img.onload = () => {
                // Bild gefunden - anzeigen
                this.showAlbumCover(albumCover, miniAlbumCover, coverPath);
            };

            img.onerror = () => {
                // Nächstes Format versuchen
                formatIndex++;
                tryLoadImage();
            };

            img.src = coverPath;
        };

        tryLoadImage();
    }

    showAlbumCover(albumCover, miniAlbumCover, coverPath) {
        // Hauptplayer Album-Cover
        albumCover.style.backgroundImage = `url('${coverPath}')`;
        albumCover.style.backgroundSize = 'cover';
        albumCover.style.backgroundPosition = 'center';
        albumCover.style.backgroundRepeat = 'no-repeat';
        albumCover.innerHTML = '';
        albumCover.classList.add('has-cover');

        // Mini-Player Album-Cover
        if (miniAlbumCover) {
            miniAlbumCover.style.backgroundImage = `url('${coverPath}')`;
            miniAlbumCover.style.backgroundSize = 'cover';
            miniAlbumCover.style.backgroundPosition = 'center';
            miniAlbumCover.style.backgroundRepeat = 'no-repeat';
            miniAlbumCover.innerHTML = '';
            miniAlbumCover.classList.add('has-cover');
        }
    }

    showPlaceholderCover(albumCover, miniAlbumCover) {
        // Hauptplayer Platzhalter
        albumCover.style.backgroundImage = '';
        albumCover.classList.remove('has-cover');
        albumCover.innerHTML = `
            <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
            </svg>
        `;

        // Mini-Player Platzhalter
        if (miniAlbumCover) {
            miniAlbumCover.style.backgroundImage = '';
            miniAlbumCover.classList.remove('has-cover');
            miniAlbumCover.innerHTML = `
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                </svg>
            `;
        }
    }

    loadMobileAlbumCover(mobileAlbumCover, songNumber) {
        const possibleFormats = ['png', 'jpg', 'jpeg', 'webp'];
        let formatIndex = 0;

        const tryLoadImage = () => {
            if (formatIndex >= possibleFormats.length) {
                // Kein Bild gefunden - Platzhalter anzeigen
                mobileAlbumCover.style.backgroundImage = '';
                mobileAlbumCover.classList.remove('has-cover');
                mobileAlbumCover.innerHTML = `
                    <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                    </svg>
                `;
                return;
            }

            const format = possibleFormats[formatIndex];
            const coverPath = `bilder/album-covers/${songNumber}.${format}`;

            const img = new Image();
            img.onload = () => {
                // Bild gefunden - anzeigen
                mobileAlbumCover.style.backgroundImage = `url('${coverPath}')`;
                mobileAlbumCover.style.backgroundSize = 'cover';
                mobileAlbumCover.style.backgroundPosition = 'center';
                mobileAlbumCover.style.backgroundRepeat = 'no-repeat';
                mobileAlbumCover.innerHTML = '';
                mobileAlbumCover.classList.add('has-cover');
            };

            img.onerror = () => {
                // Nächstes Format versuchen
                formatIndex++;
                tryLoadImage();
            };

            img.src = coverPath;
        };

        tryLoadImage();
    }

    loadModalAlbumCover(modalAlbumCover, songNumber) {
        const possibleFormats = ['png', 'jpg', 'jpeg', 'webp'];
        let formatIndex = 0;

        const tryLoadImage = () => {
            if (formatIndex >= possibleFormats.length) {
                // Kein Bild gefunden - Platzhalter anzeigen
                modalAlbumCover.style.backgroundImage = '';
                modalAlbumCover.classList.remove('has-cover');
                modalAlbumCover.innerHTML = `
                    <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                    </svg>
                `;
                return;
            }

            const format = possibleFormats[formatIndex];
            const coverPath = `bilder/album-covers/${songNumber}.${format}`;

            const img = new Image();
            img.onload = () => {
                // Bild gefunden - anzeigen
                modalAlbumCover.style.backgroundImage = `url('${coverPath}')`;
                modalAlbumCover.style.backgroundSize = 'cover';
                modalAlbumCover.style.backgroundPosition = 'center';
                modalAlbumCover.style.backgroundRepeat = 'no-repeat';
                modalAlbumCover.innerHTML = '';
                modalAlbumCover.classList.add('has-cover');
            };

            img.onerror = () => {
                // Nächstes Format versuchen
                formatIndex++;
                tryLoadImage();
            };

            img.src = coverPath;
        };

        tryLoadImage();
    }

    updatePlayingState() {
        // Alle Zeilen zurücksetzen
        document.querySelectorAll('.song-table tbody tr').forEach(row => {
            row.classList.remove('playing');
        });
        document.querySelectorAll('.mobile-song-item').forEach(item => {
            item.classList.remove('playing');
        });

        // Aktuelle Zeile markieren
        const currentRow = document.querySelector(`[data-song-id="${this.songs[this.currentSongIndex].id}"]`);
        if (currentRow) {
            currentRow.classList.add('playing');
        }
    }

    togglePlayPause() {
        if (this.currentSongIndex === -1) {
            this.playSong(0);
            return;
        }

        if (this.isPlaying) {
            this.audio.pause();
            this.isPlaying = false;
        } else {
            this.audio.play().then(() => {
                this.isPlaying = true;
            }).catch(error => {
                console.error('Fehler beim Abspielen:', error);
            });
        }

        this.updatePlayButton();
    }

    updatePlayButton() {
        const playIcons = document.querySelectorAll('.play-icon');
        const pauseIcons = document.querySelectorAll('.pause-icon');

        if (this.isPlaying) {
            playIcons.forEach(icon => icon.style.display = 'none');
            pauseIcons.forEach(icon => icon.style.display = 'block');
        } else {
            playIcons.forEach(icon => icon.style.display = 'block');
            pauseIcons.forEach(icon => icon.style.display = 'none');
        }
    }

    previousSong() {
        if (this.currentSongIndex <= 0) {
            this.playSong(this.songs.length - 1);
        } else {
            this.playSong(this.currentSongIndex - 1);
        }
    }

    nextSong() {
        if (this.isRepeatMode && this.currentSongIndex !== -1) {
            this.playSong(this.currentSongIndex);
            return;
        }

        if (this.isShuffleMode) {
            const randomIndex = Math.floor(Math.random() * this.songs.length);
            this.playSong(randomIndex);
        } else {
            if (this.currentSongIndex >= this.songs.length - 1) {
                this.playSong(0);
            } else {
                this.playSong(this.currentSongIndex + 1);
            }
        }
    }

    toggleShuffle() {
        this.isShuffleMode = !this.isShuffleMode;
        const shuffleBtn = document.getElementById('shuffle-btn');
        const mobileShuffleBtn = document.getElementById('mobile-shuffle-btn');
        shuffleBtn.classList.toggle('active', this.isShuffleMode);
        mobileShuffleBtn.classList.toggle('active', this.isShuffleMode);
    }

    toggleRepeat() {
        this.isRepeatMode = !this.isRepeatMode;
        const repeatBtn = document.getElementById('repeat-btn');
        const mobileRepeatBtn = document.getElementById('mobile-repeat-btn');
        repeatBtn.classList.toggle('active', this.isRepeatMode);
        mobileRepeatBtn.classList.toggle('active', this.isRepeatMode);
    }

    updateProgress() {
        if (this.audio.duration) {
            const progress = (this.audio.currentTime / this.audio.duration) * 100;
            this.progressFill.style.width = `${progress}%`;

            this.currentTimeDisplay.textContent = this.formatTime(this.audio.currentTime);
        }
    }

    updateDuration() {
        this.totalTimeDisplay.textContent = this.formatTime(this.audio.duration);
    }

    formatTime(seconds) {
        if (isNaN(seconds)) return '0:00';
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    seekTo(event) {
        const progressContainer = event.currentTarget;
        const rect = progressContainer.getBoundingClientRect();
        const clickX = event.clientX - rect.left;
        const percentage = clickX / rect.width;

        if (this.audio.duration) {
            this.audio.currentTime = percentage * this.audio.duration;
        }
    }

    toggleSongVisibility(songId) {
        const song = this.songs.find(s => s.id === songId);
        if (song) {
            song.isHidden = !song.isHidden;
            this.renderSongTable();
            this.renderMobileSongList();
        }
    }

    toggleAllVisibility() {
        const allHidden = this.songs.every(song => song.isHidden);
        this.songs.forEach(song => {
            song.isHidden = !allHidden;
        });
        this.renderSongTable();
        this.renderMobileSongList();
    }

    downloadSong(songId) {
        const song = this.songs.find(s => s.id === songId);
        if (song) {
            const link = document.createElement('a');
            link.href = `audio/${song.dateiname}`;
            link.download = `${song.titel} - Jana Breitmar.wav`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    downloadCurrent() {
        if (this.currentSongIndex !== -1) {
            this.downloadSong(this.songs[this.currentSongIndex].id);
        }
    }

    downloadAllVisible() {
        const visibleSongs = this.filteredSongs.filter(song => !song.isHidden);

        if (visibleSongs.length === 0) {
            alert('Keine sichtbaren Lieder zum Herunterladen.');
            return;
        }

        // Sequenzieller Download mit kleiner Verzögerung
        visibleSongs.forEach((song, index) => {
            setTimeout(() => {
                this.downloadSong(song.id);
            }, index * 500); // 500ms Verzögerung zwischen Downloads
        });
    }

    openMobilePlayer() {
        this.modalOverlay.style.display = 'flex';
        this.miniPlayer.style.display = 'none'; // Mini-Player verstecken wenn Modal geöffnet wird

        // Player-Inhalt in Modal kopieren
        const desktopPlayer = document.querySelector('.player-container');
        const modalContent = document.querySelector('.modal-content');
        modalContent.innerHTML = desktopPlayer.innerHTML;

        // Album-Cover auch im Modal aktualisieren
        if (this.currentSongIndex !== -1) {
            const song = this.songs[this.currentSongIndex];
            const modalAlbumCover = modalContent.querySelector('#album-cover');
            if (modalAlbumCover) {
                // Verwende die gleiche Logik wie im Hauptplayer
                this.loadModalAlbumCover(modalAlbumCover, song.nummer);
            }
        }

        // Event Listeners für Modal-Player neu setzen
        this.setupModalEventListeners();
    }

    closeMobilePlayer() {
        this.modalOverlay.style.display = 'none';
        // Mini-Player anzeigen wenn ein Song spielt
        if (this.currentSongIndex !== -1) {
            this.miniPlayer.style.display = 'flex';
        }
    }

    handleResize() {
        // Mini-Player nur im mobilen Layout anzeigen
        if (window.innerWidth > 768) {
            this.miniPlayer.style.display = 'none';
        } else if (this.currentSongIndex !== -1 && this.modalOverlay.style.display !== 'flex') {
            this.miniPlayer.style.display = 'flex';
        }
    }

    setupModalEventListeners() {
        const modalContent = document.querySelector('.modal-content');

        // Play/Pause Button
        const modalPlayPause = modalContent.querySelector('#play-pause-btn');
        if (modalPlayPause) {
            modalPlayPause.addEventListener('click', () => this.togglePlayPause());
        }

        // Previous/Next Buttons
        const modalPrev = modalContent.querySelector('#prev-btn');
        const modalNext = modalContent.querySelector('#next-btn');
        if (modalPrev) modalPrev.addEventListener('click', () => this.previousSong());
        if (modalNext) modalNext.addEventListener('click', () => this.nextSong());

        // Progress Bar
        const modalProgressContainer = modalContent.querySelector('.progress-bar-container');
        if (modalProgressContainer) {
            modalProgressContainer.addEventListener('click', (e) => this.seekTo(e));
        }
    }

    handleAudioError(error) {
        console.error('Audio Fehler:', error);

        // Benutzerfreundliche Fehlermeldung
        const currentSong = this.songs[this.currentSongIndex];
        const errorMessage = `Fehler beim Laden von "${currentSong?.titel || 'Unbekanntes Lied'}".
                             Möglicherweise ist die Audiodatei nicht verfügbar.`;

        // Fehler in der UI anzeigen statt Alert
        this.showErrorMessage(errorMessage);

        this.isPlaying = false;
        this.updatePlayButton();

        // Automatisch zum nächsten Lied springen nach 3 Sekunden
        setTimeout(() => {
            if (this.songs.length > 1) {
                this.nextSong();
            }
        }, 3000);
    }

    showErrorMessage(message) {
        // Temporäre Fehlermeldung in der Lyrics-Box anzeigen
        const originalContent = this.lyricsContent.innerHTML;
        this.lyricsContent.innerHTML = `
            <div style="color: #ff4444; padding: 10px; background: #fff5f5; border-radius: 4px; border-left: 4px solid #ff4444;">
                <strong>Fehler:</strong><br>
                ${message}
            </div>
        `;

        // Nach 5 Sekunden wieder original Inhalt anzeigen
        setTimeout(() => {
            this.lyricsContent.innerHTML = originalContent;
        }, 5000);
    }

    handleKeyboard(event) {
        // Nur reagieren wenn kein Input-Element fokussiert ist
        if (event.target.tagName === 'INPUT' || event.target.tagName === 'SELECT') return;

        switch (event.code) {
            case 'Space':
                event.preventDefault();
                this.togglePlayPause();
                break;
            case 'ArrowLeft':
                event.preventDefault();
                this.previousSong();
                break;
            case 'ArrowRight':
                event.preventDefault();
                this.nextSong();
                break;
            case 'KeyS':
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    this.toggleShuffle();
                }
                break;
            case 'KeyR':
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    this.toggleRepeat();
                }
                break;
        }
    }

    setupLyricsTabs() {
        // Event Listener für Tab-Buttons
        document.querySelectorAll('.lyrics-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabType = e.target.dataset.tab;
                this.switchLyricsTab(tabType);
            });
        });
    }

    switchLyricsTab(tabType) {
        // Tab-Buttons aktualisieren
        document.querySelectorAll('.lyrics-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        const targetTab = document.querySelector(`[data-tab="${tabType}"]`);
        if (targetTab) {
            targetTab.classList.add('active');
        }

        // Alle Content-Container verstecken
        this.lyricsContent.style.display = 'none';
        this.lyricsTranslation.style.display = 'none';
        this.lyricsInfo.style.display = 'none';

        // Gewählten Content anzeigen
        switch(tabType) {
            case 'lyrics':
                this.lyricsContent.style.display = 'block';
                break;
            case 'translation':
                this.lyricsTranslation.style.display = 'block';
                break;
            case 'info':
                this.lyricsInfo.style.display = 'block';
                break;
        }
    }

    hasTranslation(song) {
        // Prüft ob ein Song eine deutsche Übersetzung hat
        return song.translation && song.translation.trim();
    }

    hasInfo(song) {
        // Prüft ob ein Song zusätzliche Informationen hat
        return song.info && song.info.trim();
    }

    updateLyricsDisplay(song) {
        // Original Lyrics anzeigen
        if (song.lyrics && song.lyrics.trim()) {
            let formattedLyrics = this.formatLyrics(song.lyrics);
            this.lyricsContent.innerHTML = `<p>${formattedLyrics}</p>`;
        } else {
            this.lyricsContent.innerHTML = '<p class="no-lyrics">Keine Lyrics verfügbar für dieses Lied.</p>';
        }

        // Übersetzung anzeigen (falls vorhanden)
        if (this.hasTranslation(song)) {
            let formattedTranslation = this.formatLyrics(song.translation);
            this.lyricsTranslation.innerHTML = `<p>${formattedTranslation}</p>`;
        } else {
            this.lyricsTranslation.innerHTML = '<p class="no-lyrics">Keine deutsche Übersetzung verfügbar.</p>';
        }

        // Info anzeigen (falls vorhanden)
        if (this.hasInfo(song)) {
            let formattedInfo = this.formatLyrics(song.info);
            this.lyricsInfo.innerHTML = `<p>${formattedInfo}</p>`;
        } else {
            this.lyricsInfo.innerHTML = '<p class="no-lyrics">Keine zusätzlichen Informationen verfügbar.</p>';
        }

        // Tabs konfigurieren
        this.configureTabs(song);
    }

    configureTabs(song) {
        const hasTranslation = this.hasTranslation(song);
        const hasInfo = this.hasInfo(song);

        // Tab-Buttons anzeigen/verstecken
        const translationTab = document.querySelector('[data-tab="translation"]');
        const infoTab = document.querySelector('[data-tab="info"]');

        if (hasTranslation) {
            translationTab.style.display = 'block';
        } else {
            translationTab.style.display = 'none';
        }

        if (hasInfo) {
            infoTab.style.display = 'block';
        } else {
            infoTab.style.display = 'none';
        }

        // Tabs sind immer sichtbar (mindestens "Lyrics")
        this.lyricsTabs.style.display = 'flex';

        // Standard: Lyrics Tab aktiv
        this.switchLyricsTab('lyrics');
    }

    formatLyrics(lyrics) {
        // Lyrics formatieren: Doppelte Zeilenumbrüche = neue Absätze, einzelne = <br>
        let formatted = lyrics.trim();

        // Erst alle doppelten Zeilenumbrüche durch einen Platzhalter ersetzen
        formatted = formatted.replace(/\n\n+/g, '|||PARAGRAPH|||');

        // Dann einzelne Zeilenumbrüche durch <br> ersetzen
        formatted = formatted.replace(/\n/g, '<br>');

        // Platzhalter durch Absatz-Enden und -Anfänge ersetzen
        formatted = formatted.replace(/\|\|\|PARAGRAPH\|\|\|/g, '</p><p>');

        return formatted;
    }
}

// Player initialisieren wenn DOM geladen ist
document.addEventListener('DOMContentLoaded', () => {
    window.musikPlayer = new MusikPlayer();
});

